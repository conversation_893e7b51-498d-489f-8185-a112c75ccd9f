import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth-utils';
import { getProductDescriptionFromImage } from '@/utils/replicate';

// POST /api/admin/products/bulk/metadata - Extract metadata from multiple images using Gemini
export async function POST(request: NextRequest) {
  try {
    // Check admin access
    await requireAdmin();

    const body = await request.json();
    const { images } = body;

    if (!images || !Array.isArray(images) || images.length === 0) {
      return NextResponse.json(
        { success: false, error: "Images array is required" },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    // Process each image with Gemini AI
    for (let i = 0; i < images.length; i++) {
      const imageUrl = images[i];
      
      try {
        console.log(`🔍 Analyzing image ${i + 1}/${images.length}: ${imageUrl}`);
        
        // Convert URL to base64
        const response = await fetch(imageUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType?.startsWith('image/')) {
          throw new Error('Invalid content type: Must be an image');
        }

        const arrayBuffer = await response.arrayBuffer();
        const base64Data = Buffer.from(arrayBuffer).toString('base64');
        const base64Image = `data:${contentType};base64,${base64Data}`;
        
        // Analyze with Gemini
        const description = await getProductDescriptionFromImage(base64Image);
        
        if (description) {
          try {
            const parsed = JSON.parse(description);
            results.push({
              imageUrl,
              success: true,
              data: parsed
            });
            console.log(`✅ Successfully analyzed image ${i + 1}: ${parsed.name || 'Unknown product'}`);
          } catch (parseError) {
            console.error(`❌ Failed to parse JSON for image ${i + 1}:`, parseError);
            errors.push(`Image ${i + 1}: Failed to parse AI response`);
            results.push({
              imageUrl,
              success: false,
              error: 'Failed to parse AI response'
            });
          }
        } else {
          console.error(`❌ No description returned for image ${i + 1}`);
          errors.push(`Image ${i + 1}: No description generated`);
          results.push({
            imageUrl,
            success: false,
            error: 'No description generated'
          });
        }
        
        // Add a small delay to avoid rate limiting
        if (i < images.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
      } catch (error) {
        console.error(`❌ Error processing image ${i + 1}:`, error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Image ${i + 1}: ${errorMessage}`);
        results.push({
          imageUrl,
          success: false,
          error: errorMessage
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    console.log(`📊 Bulk analysis complete: ${successCount} successful, ${failureCount} failed`);

    return NextResponse.json({
      success: successCount > 0,
      data: {
        results,
        summary: {
          total: images.length,
          successful: successCount,
          failed: failureCount
        }
      },
      errors: errors.length > 0 ? errors : undefined,
      message: `Analyzed ${images.length} images: ${successCount} successful, ${failureCount} failed`
    });

  } catch (error) {
    console.error('Error in bulk metadata extraction:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to extract metadata from images',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
